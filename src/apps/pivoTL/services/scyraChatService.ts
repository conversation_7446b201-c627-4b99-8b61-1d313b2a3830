import { useCallback } from 'react';
import { usePivotlPrivateRequest } from '../../../lib/axios/usePrivateRequest';
import { agenticService } from '../../../utils/apiServiceControllersRoute';
import { BASE_URL } from '../../../utils/apiUrls';
import { useActiveTenant } from '../context/ActiveTenantContext';
import { useTenant } from '../context/TenantContext';

const CHAT_ENDPOINT = `${agenticService}/ai-agents/chat`;
const HISTORY_ENDPOINT = `${CHAT_ENDPOINT}/history`;

export interface ScyraChatRequest {
  userMessage: string;
  sessionId: string;
}

export interface ChatHistoryItem {
  userId: string;
  conversationId: string;
  message: string;
  sender: string;
  createdAt: string;
}

type ApiResponse<T> = Promise<T>;

const useScyraApi = () => {
  const { selectedTenant } = useTenant();
  const { activeAgent } = useActiveTenant();
  // const tenantId = activeTenant?.tenantId ?? '';
  const tenantId = selectedTenant?.tenant?.tenantId || '';
  const agentKey = activeAgent ?? '';

  return usePivotlPrivateRequest(BASE_URL, tenantId, agentKey);
};

// Scyra chat hook
export const useScyraChatApi = () => {
  const pivotlRequestRef = useScyraApi();

  const chatWithScyra = useCallback(
    async (payload: ScyraChatRequest): ApiResponse<string> => {
      try {
        const axiosInstance = pivotlRequestRef.current;
        if (!axiosInstance) {
          throw new Error('Axios instance not initialized');
        }

        const { data } = await axiosInstance.post(CHAT_ENDPOINT, payload);
        return data || '';
      } catch (error) {
        // Try to surface agent name if present on the axios instance headers or fallback
        const agentName =
          pivotlRequestRef.current?.defaults?.headers?.['X-Active-Agent'] ||
          'agent';
        console.error(`${agentName} Chat API Error:`, error);
        throw new Error(
          `Failed to communicate with ${agentName}. Please try again.`,
        );
      }
    },
    [pivotlRequestRef],
  );

  return chatWithScyra;
};

// Chat history hook
export const useScyraChatHistoryApi = () => {
  const pivotlRequestRef = useScyraApi();

  const fetchChatHistory = useCallback(async (): ApiResponse<
    ChatHistoryItem[]
  > => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }

      const { data } = await axiosInstance.get(HISTORY_ENDPOINT);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      throw new Error('Failed to fetch chat history');
    }
  }, [pivotlRequestRef]);

  return fetchChatHistory;
};
