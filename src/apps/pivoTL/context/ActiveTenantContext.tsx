import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { TenantInfo } from '../types/user';
import { usePivotlAuth } from './PivotlAuthContext';

interface ActiveTenantContextType {
  activeTenant: TenantInfo | null;
  setActiveTenant: (tenant: TenantInfo | null) => void;
  clearActiveTenant: () => void;
  isActiveTenantLoading: boolean;
  // Helper methods using centralized tenant data
  getCurrentTenant: () => TenantInfo | null;
  getAllTenants: () => TenantInfo[] | null;
  setActiveTenantById: (tenantId: string) => void;
  // Active agent state
  activeAgent: string;
  setActiveAgent: (agentKey: string) => void;
}

const ActiveTenantContext = createContext<ActiveTenantContextType | undefined>(
  undefined,
);

const STORAGE_KEY = 'pivotl_active_tenant';
const STORAGE_EXPIRY_KEY = 'pivotl_active_tenant_expiry';
const AGENT_STORAGE_KEY = 'pivotl_active_agent';
const TENANT_EXPIRY_HOURS = 24; // Expire tenant selection after 24 hours for security

interface ActiveTenantProviderProps {
  children: ReactNode;
}

export const ActiveTenantProvider: React.FC<ActiveTenantProviderProps> = ({
  children,
}) => {
  const { tenants, isLoadingUserInitials } = usePivotlAuth();
  const [activeTenant, setActiveTenantState] = useState<TenantInfo | null>(
    null,
  );
  const [isActiveTenantLoading, setIsActiveTenantLoading] = useState(true);
  const [activeAgent, setActiveAgentState] = useState<string>('colton');

  // Load active tenant and agent from localStorage on mount and sync with centralized tenant data
  useEffect(() => {
    const loadActiveTenant = () => {
      try {
        const storedTenantId = getActiveTenantId();

        if (storedTenantId && tenants) {
          // Find the tenant in the centralized list
          const foundTenant = tenants.find(t => t.tenantId === storedTenantId);
          if (foundTenant) {
            setActiveTenantState(foundTenant);
          } else {
            // Stored tenant ID not found in user's tenants, clear it
            localStorage.removeItem(STORAGE_KEY);
            localStorage.removeItem(STORAGE_EXPIRY_KEY);
          }
        }
      } catch (error) {
        console.error('Error loading active tenant from localStorage:', error);
        // Clear corrupted data
        localStorage.removeItem(STORAGE_KEY);
        localStorage.removeItem(STORAGE_EXPIRY_KEY);
      } finally {
        setIsActiveTenantLoading(false);
      }
    };

    const loadActiveAgent = () => {
      try {
        const storedAgent = localStorage.getItem(AGENT_STORAGE_KEY);
        if (storedAgent) {
          setActiveAgentState(storedAgent);
        }
      } catch (error) {
        console.error('Error loading active agent from localStorage:', error);
        localStorage.removeItem(AGENT_STORAGE_KEY);
      }
    };

    if (!isLoadingUserInitials) {
      loadActiveTenant();
      loadActiveAgent();
    }
  }, [tenants, isLoadingUserInitials]);

  const handleSetActiveTenant = (tenant: TenantInfo | null) => {
    setActiveTenantState(tenant);

    try {
      if (tenant) {
        // Set expiry time
        const expiryTime = new Date();
        expiryTime.setHours(expiryTime.getHours() + TENANT_EXPIRY_HOURS);

        localStorage.setItem(STORAGE_KEY, JSON.stringify(tenant));
        localStorage.setItem(STORAGE_EXPIRY_KEY, expiryTime.toISOString());
      } else {
        localStorage.removeItem(STORAGE_KEY);
        localStorage.removeItem(STORAGE_EXPIRY_KEY);
      }
    } catch (error) {
      console.error('Error saving active tenant to localStorage:', error);
    }
  };

  // Helper methods using centralized tenant data
  const getCurrentTenant = () => {
    if (!activeTenant || !tenants) return null;
    // Always return the latest tenant data from centralized source
    return tenants.find(t => t.tenantId === activeTenant.tenantId) || null;
  };

  const getAllTenants = () => tenants;

  const setActiveTenantById = (tenantId: string) => {
    if (!tenants) return;
    const tenant = tenants.find(t => t.tenantId === tenantId);
    if (tenant) {
      handleSetActiveTenant(tenant);
    }
  };

  const handleClearActiveTenant = () => {
    setActiveTenantState(null);
    try {
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STORAGE_EXPIRY_KEY);
    } catch (error) {
      console.error('Error clearing active tenant from localStorage:', error);
    }
  };

  const handleSetActiveAgent = (agentKey: string) => {
    setActiveAgentState(agentKey);
    try {
      if (agentKey) {
        localStorage.setItem(AGENT_STORAGE_KEY, agentKey);
      } else {
        localStorage.removeItem(AGENT_STORAGE_KEY);
      }
    } catch (error) {
      console.error('Error saving active agent to localStorage:', error);
    }
  };

  const value: ActiveTenantContextType = {
    activeTenant,
    setActiveTenant: handleSetActiveTenant,
    clearActiveTenant: handleClearActiveTenant,
    isActiveTenantLoading: isActiveTenantLoading || isLoadingUserInitials,
    getCurrentTenant,
    getAllTenants,
    setActiveTenantById,
    activeAgent,
    setActiveAgent: handleSetActiveAgent,
  };

  return (
    <ActiveTenantContext.Provider value={value}>
      {children}
    </ActiveTenantContext.Provider>
  );
};

export const useActiveTenant = (): ActiveTenantContextType => {
  const context = useContext(ActiveTenantContext);
  if (!context) {
    throw new Error(
      'useActiveTenant must be used within an ActiveTenantProvider',
    );
  }
  return context;
};

// Helper function to get active tenant ID for API headers
export const getActiveTenantId = (): string | null => {
  try {
    const storedTenant = localStorage.getItem(STORAGE_KEY);
    const storedExpiry = localStorage.getItem(STORAGE_EXPIRY_KEY);

    if (storedTenant && storedExpiry) {
      const expiryTime = new Date(storedExpiry).getTime();
      const currentTime = new Date().getTime();

      if (currentTime < expiryTime) {
        const parsedTenant = JSON.parse(storedTenant);
        return parsedTenant.tenantId;
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting active tenant ID:', error);
    return null;
  }
};

export default ActiveTenantProvider;
