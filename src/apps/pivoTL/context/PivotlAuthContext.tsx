import React, { createContext, useContext, useEffect, useState } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import { isDevEnvironment } from '@/features/UserOnboarding/utils/helper';
import { useGetPivotlUser } from '../hooks/useUser';
import {
  PivotlUserBasicInfoPayload,
  PivotlUserInfo,
  TenantInfo,
} from '../types/user';
import { useTenant } from './TenantContext';

interface PivotlAuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  isError: boolean;
  isLoadingUserInitials: boolean;
  isOnline: boolean;
  user: PivotlUserInfo | null;
  tenants: TenantInfo[] | null;
  login: () => void;
  logout: () => void;
  getUserInitials: () => string;
}

const PivotlAuthContext = createContext<PivotlAuthContextType | null>(null);

export const usePivotlAuth = () => {
  const context = useContext(PivotlAuthContext);
  if (!context) {
    throw new Error('usePivotlAuth must be used within a PivotlAuthProvider');
  }
  return context;
};

interface PivotlAuthProviderProps {
  children: React.ReactNode;
}

export const PivotlAuthProvider: React.FC<PivotlAuthProviderProps> = ({
  children,
}) => {
  const { keycloak, initialized } = useKeycloak();
  const [isReady, setIsReady] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { setSelectedTenant, setTenants } = useTenant();

  const isDev = isDevEnvironment();

  // Network connectivity detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const {
    data: userData,
    isLoading: userDataLoading,
    isError,
    refetch,
  } = useGetPivotlUser<PivotlUserBasicInfoPayload>({
    enabled: isDev || (initialized && keycloak.authenticated && isOnline),
    retry: isOnline ? 3 : 0,
    refetchOnReconnect: true,
    networkMode: isOnline ? 'online' : 'offlineFirst',
  });

  useEffect(() => {
    if (isDev) {
      setIsReady(true);
    } else if (initialized) {
      setIsReady(true);
      if (keycloak.authenticated && !userData && !userDataLoading) {
        refetch();
      }
    }
  }, [
    initialized,
    keycloak.authenticated,
    userData,
    userDataLoading,
    refetch,
    isDev,
  ]);

  // Set tenants when user data is loaded
  useEffect(() => {
    if (userData?.tenants && userData.tenants.length > 0) {
      const tenants = userData.tenants.map(tenant => ({
        id: tenant.tenantId,
        agentSuiteKey: tenant.tenantAgentSuite.agentSuiteKey,
        name: tenant.tenantAgentSuite.agentSuiteName,
        logo: tenant.tenantAgentSuite.avatar,
        tenant: tenant,
      }));

      setTenants(tenants);

      // Set the first tenant as the default if none is selected
      if (tenants.length > 0) {
        setSelectedTenant(tenants[0]);
      }
    }
  }, [userData?.tenants, setTenants, setSelectedTenant]);

  const login = () => {
    keycloak.login({
      redirectUri: `${window.location.origin}/pivotl/dashboard`,
    });
  };

  const logout = () => {
    // Clear all tenant-related localStorage data
    try {
      localStorage.removeItem('pivotl_selected_tenant');
      localStorage.removeItem('pivotl_active_tenant');
      localStorage.removeItem('pivotl_active_tenant_expiry');
      localStorage.removeItem('pivotl_active_agent');
    } catch (error) {
      console.error('Error clearing localStorage on logout:', error);
    }

    keycloak.logout({
      redirectUri: `${window.location.origin}/pivotl`,
    });
  };

  const getUserInitials = (): string => {
    if (!userData?.userInfo) return 'U';

    const { firstName, lastName } = userData.userInfo;

    if (firstName && lastName) {
      return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
    }

    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }

    if (lastName) {
      return lastName.charAt(0).toUpperCase();
    }

    if (userData.userInfo.email) {
      return userData.userInfo.email.charAt(0).toUpperCase();
    }

    return 'U';
  };

  const value: PivotlAuthContextType = {
    isAuthenticated: isDev
      ? !!userData
      : initialized &&
        (keycloak.authenticated ?? false) &&
        !!userData &&
        isOnline,
    isLoading: !isReady || userDataLoading,
    isLoadingUserInitials: userDataLoading,
    isError: isError || (!isOnline && !isDev),
    isOnline,
    user: userData?.userInfo || null,
    tenants: userData?.tenants || null,
    login,
    logout,
    getUserInitials,
  };

  return (
    <PivotlAuthContext.Provider value={value}>
      {children}
    </PivotlAuthContext.Provider>
  );
};
