import React, { useState } from 'react';
import { AgentCard } from '../PivotlAiAgentsPage';
import { ROUTES } from '../../constants/routes';
import clsx from 'clsx';
import { dashboardIcons } from '../../assets/images';
import { useGetAIAgentsData } from '../../hooks/useAIAgents';
import { useTenant } from '../../context/TenantContext';
import { Link } from 'react-router-dom';
import AgentSuiteSkeletonLoader from '../../components/ui/AgentSuiteSkeleton';
import { useActiveTenant } from '../../context/ActiveTenantContext';
import { agentSuites as mockAgentsSuites } from '../../data/constants';

type DashboardTab = { label: string; value: string };

interface DashboardWelcomeProps {
  onServiceSelect?: () => void;
}

const DashboardWelcome: React.FC<DashboardWelcomeProps> = () => {
  const [activeTab, setActiveTab] = useState<DashboardTab>({
    label: 'ai-agent-suite-dashboard',
    value: 'AI Agent Suite dashboard',
  });
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites, error } =
    useGetAIAgentsData();
  const { setActiveAgent } = useActiveTenant();
  const { tenants } = useTenant();

  // Check if user has claimed the specific agent suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return (
      tenants &&
      tenants.length > 0 &&
      tenants.some(tenant => tenant.agentSuiteKey === suiteKey)
    );
  };

  // Handle agent suite card click
  const redirectToSuite = (suite: any) => {
    if (isAgentSuiteClaimed(suite.agentSuiteKey)) {
      // If claimed, redirect to analytics dashboard
      return ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD;
    } else {
      // If not claimed, go to activation page
      return ROUTES.DASHBOARD_ANALYTICS_ACTIVATE_SUITE(suite.agentSuiteKey);
    }
  };

  const tabs: DashboardTab[] = [
    { label: 'ai-agent-suite-dashboard', value: 'Agent Suites Dashboard' },
    { label: 'agentic-ai-dashboard', value: 'Agents Dashboard' },
  ];

  return (
    <div className="flex h-full flex-col gap-8 p-8">
      {/* Header */}
      <div className="text-start">
        <h1 className="mb-2 text-2xl font-semibold text-blackOne">Dashboard</h1>

        {/* Hero Section */}
        <div className="flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-[#040721] text-white">
          <div className="flex w-full items-center justify-between">
            <div className="p-6 text-left">
              <h2 className="mb-2 text-lg font-bold">
                Actionable Intelligence across all Agentic AI agents.
              </h2>
              <p className="text-gray-300">
                Compare performance, monitor activity, and act on daily
                insights.
              </p>
            </div>
            <div className="relative mr-8 h-full w-[158px]">
              <img src={dashboardIcons} alt="bg" className="h-full w-full" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.label}
            onClick={() => setActiveTab(tab)}
            className={clsx(
              'px-4 py-2 text-sm font-medium transition-colors',
              activeTab.label === tab.label
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-600 hover:text-blackOne',
            )}
          >
            {tab.value}
          </button>
        ))}
      </div>

      {/* Error State */}
      {error && (
        <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-700">
          <p>Error loading agent suites: {error.message}</p>
        </div>
      )}

      {/* Content */}
      <div className="w-fit">
        {activeTab.label === 'ai-agent-suite-dashboard' && (
          <>
            {isLoadingSuites ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {agentSuites.map(suite => (
                  <Link
                    to={redirectToSuite(suite)}
                    key={suite.agentSuiteKey}
                    className={clsx(
                      'flex h-[195px] w-full max-w-[334px] items-center overflow-hidden rounded-lg border border-transparent bg-white transition-shadow hover:border-primary',
                      'cursor-pointer',
                    )}
                  >
                    {/* Agent Image */}

                    <div className="h-full w-[111px] flex-shrink-0">
                      <img
                        src={suite.avatar}
                        alt={suite.agentSuiteName}
                        className="h-full w-full object-cover"
                        onError={e => {
                          // Fallback to mock logo if agent avatar fails to load
                          (e.target as HTMLImageElement).src =
                            mockAgentsSuites.filter(
                              agent =>
                                agent.id.toLowerCase() ===
                                suite.agentSuiteKey.toLowerCase(),
                            )[0].image;
                        }}
                      />
                    </div>

                    {/* Agent Content */}
                    <div className="h-full bg-white p-4">
                      <div className="mb-2 flex items-center justify-between">
                        <h3 className="font-spartan text-base font-bold text-blackOne">
                          {suite.agentSuiteName}
                        </h3>
                      </div>
                      <h4 className="mb-2 font-spartan text-sm font-semibold text-subText">
                        {suite.description}
                      </h4>
                      <p className="text-sm text-subText">
                        {suite.roleDescription}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </>
        )}

        {activeTab.label === 'agentic-ai-dashboard' && (
          <>
            {isLoadingAgents ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {agents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    className="w-full max-w-[334px]"
                    agent={agent}
                    link={ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(
                      agent.agentKey,
                    )}
                    onAgentSelect={() => setActiveAgent(agent.agentKey)}
                  />
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default DashboardWelcome;
