import React, { useEffect, useRef, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/apps/pivoTL/data/constants';
import {
  AIAgent,
  useClaimAgentSuiteApi,
} from '@/apps/pivoTL/services/upivotalAgenticService';
import { useGetAIAgentSuites } from '@/apps/pivoTL/hooks/useAIAgents';
import EnhancedChatSidebar from '@/apps/pivoTL/components/common/EnhancedChatSidebar';
import { useActiveTenant } from '@/apps/pivoTL/context/ActiveTenantContext';
import { useTenant } from '@/apps/pivoTL/context/TenantContext';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { AgentCard } from '../../PivotlAiAgentsPage';
import { claimIcon } from '@/apps/pivoTL/assets/icons';

const DashboardActivatePage: React.FC = () => {
  const { suiteId } = useParams<{ suiteId: string }>();
  const navigate = useNavigate();

  // Fetch agent suites data
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();

  // Find the suite by ID
  const suite = agentSuites.find(s => s.agentSuiteKey === suiteId);

  // Get agents that belong to this suite
  const suiteAgents: AIAgent[] = suite?.availableAgents || [];
  const suiteFallbackImage = mockAgentsSuite.filter(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase(),
  )[0]?.image;

  const [isLoading, setIsLoading] = useState(false);
  const [chatErrorMessage, setChatMessage] = useState<string>('');
  const { activeAgent, setActiveAgent } = useActiveTenant();
  const { tenants, setTenants, setSelectedTenant } = useTenant();

  const claimAgentsSuite = useClaimAgentSuiteApi();

  // Check if the current agent suite is already claimed
  const isAgentSuiteClaimed = tenants.some(
    tenant => tenant.agentSuiteKey === suite?.agentSuiteKey,
  );

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  const handleClaimAgentSuite = async (agentSuiteKey: string) => {
    try {
      setIsLoading(true);
      // Clear any previous error messages
      setChatMessage('');

      const response = await claimAgentsSuite(agentSuiteKey);
      if (response.status === true) {
        // Create new tenant from the response data
        const newTenant = {
          id: response.data.tenantId,
          agentSuiteKey: response.data.tenantAgentSuite.agentSuiteKey,
          name: response.data.tenantAgentSuite.agentSuiteName,
          logo: response.data.tenantAgentSuite.avatar,
          tenant: response.data, // Store full tenant data
        };

        // Update the tenant list with the new tenant
        const updatedTenants = [...tenants, newTenant];
        setTenants(updatedTenants);

        // Set the newly claimed tenant as the selected tenant
        setSelectedTenant(newTenant);

        // Display success message in chat interface
        setChatMessage(
          response.message || 'Agent suite claimed successfully!',
        );

        navigate(ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD);
      } else {
        // Display error message in chat interface
        setChatMessage(response.message || 'Failed to claim agent suite');
      }
    } catch (error: unknown) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error
          ? (error as any).response?.data?.message
          : error instanceof Error
            ? error.message
            : 'Failed to claim agent suite. Please try again.';

      // Display error message in chat interface
      setChatMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Trigger reload of chat history when agent changes
  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error('Error occured while changing agent:', error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  // Show loading state while fetching data
  if (isLoadingSuites) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading agent suite...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!suite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Agent Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agent suite with ID "{suiteId}" could not be found.
          </p>
          <button
            onClick={() => navigate(-1)}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to Agents Hub
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={chatErrorMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-col gap-y-4 p-8">
          {/* Breadcrumb */}
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-1 font-semibold text-blackTwo"
          >
            <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
            Agent Suite
          </button>

          {/* Suite Header */}
          <div className="max-w-[732px] font-spartan">
            <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: suite.avatar
                    ? `url(${suite.avatar})`
                    : `url(${suiteFallbackImage})`,
                }}
              />
              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-black/20" />
              {/* Content */}
              <div className="relative z-10 flex h-full flex-col justify-center p-6">
                <h1 className="w-fit rounded bg-white px-4 py-2 text-[32px] font-bold backdrop-blur-sm">
                  {suite.agentSuiteName}
                </h1>

                <h2 className="mt-8 w-fit rounded text-[20px] font-semibold text-white">
                  {suite.description}
                </h2>
                <div className="font-inter text-lg text-white">
                  {suite.roleDescription}
                </div>
              </div>
            </div>

            {!isAgentSuiteClaimed && (
              <div className="mb-4 mt-8 flex items-center justify-between rounded-lg border border-primary bg-peachTwo p-6">
                <div className="flex items-center gap-2">
                  <img
                    src={claimIcon}
                    alt=""
                    className="h-[46px] w-[46px] object-cover"
                  />
                  <div>
                    <div className="font-medium text-blackOne">
                      Claim Your Suite
                    </div>
                    <p className="mt-1 font-inter text-sm text-subText">
                      Start using your AI agents immediately by claiming this
                      suite
                    </p>
                  </div>
                </div>
                <button
                  className="rounded bg-primary px-4 py-3 font-normal text-white transition-colors hover:bg-orange-15 disabled:opacity-30"
                  onClick={() => handleClaimAgentSuite(suite.agentSuiteKey)}
                  disabled={isLoading}
                >
                  <div className="flex items-center gap-2">
                    {isLoading && (
                      <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                    )}
                    <span>Claim this suite</span>
                  </div>
                </button>
              </div>
            )}
          </div>

          {/* Agents Grid */}
          {suiteAgents.length > 0 && (
            <div className="grid w-fit grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {suiteAgents.map(agent => (
                <AgentCard
                  key={agent.agentKey}
                  agent={agent}
                  showChatButton={true}
                  link={
                    agent.agentKey
                      ? ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(agent.agentKey)
                      : '#'
                  }
                  onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardActivatePage;
