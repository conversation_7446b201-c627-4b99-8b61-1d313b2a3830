import React, { useEffect, useRef, useState } from 'react';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/apps/pivoTL/data/constants';
import { AgentCard } from '../index';
import {
  AIAgent,
  useClaimAgentSuiteApi,
} from '@/apps/pivoTL/services/upivotalAgenticService';
import EnhancedChatSidebar from '@/apps/pivoTL/components/common/EnhancedChatSidebar';
import { useActiveTenant } from '@/apps/pivoTL/context/ActiveTenantContext';
import { useTenant } from '@/apps/pivoTL/context/TenantContext';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

const AgentSuiteDetailPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const suite = location.state;

  // Get agents that belong to this suite
  const suiteAgents: AIAgent[] = suite?.availableAgents;
  const suiteFallbackImage = mockAgentsSuite.filter(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase(),
  )[0]?.image;

  const [isLoading, setIsLoading] = useState(false);
  const [chatMessage, setChatMessage] = useState<string>('');
  const { activeAgent, setActiveAgent } = useActiveTenant();
  const { tenants, setTenants, setSelectedTenant } = useTenant();

  const claimAgentsSuite = useClaimAgentSuiteApi();

  // Check if the current agent suite is already claimed
  const isAgentSuiteClaimed = tenants.some(
    tenant => tenant.agentSuiteKey === suite?.agentSuiteKey,
  );

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  const handleClaimAgentSuite = async (agentSuiteKey: string) => {
    try {
      setIsLoading(true);
      // Clear any previous error messages
      setChatMessage('');

      const response = await claimAgentsSuite(agentSuiteKey);

      if (response.status === true) {
        // Create new tenant from the response data
        const newTenant = {
          id: response.data.tenantId,
          agentSuiteKey: response.data.tenantAgentSuite.agentSuiteKey,
          name: response.data.tenantAgentSuite.agentSuiteName,
          logo: response.data.tenantAgentSuite.avatar,
          tenant: response.data, // Store full tenant data
        };

        // Update the tenant list with the new tenant
        const updatedTenants = [...tenants, newTenant];
        setTenants(updatedTenants);

        // Set the newly claimed tenant as the selected tenant
        setSelectedTenant(newTenant);

        // Display success message in chat interface
        setChatMessage(response.message || 'Agent suite claimed successfully!');

        navigate(ROUTES.DASHBOARD_AGENT_ACTIVATION_SUITE(suite.id));
      } else {
        // Display error message in chat interface
        setChatMessage(response.message || 'Failed to claim agent suite');
      }
    } catch (error: unknown) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error
          ? (error as any).response?.data?.message
          : error instanceof Error
            ? error.message
            : 'Failed to claim agent suite. Please try again.';

      // Display error message in chat interface
      setChatMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Trigger reload of chat history when agent changes
  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error('Error occured while changing agent:', error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  if (!suite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Agent Suite Not Found
          </h1>
          <Link
            to={ROUTES.DASHBOARD_AI_AGENTS}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to Agents Hub
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-col gap-y-4 p-8">
          {/* Breadcrumb */}
          <Link
            to={ROUTES.DASHBOARD_AI_AGENTS}
            className="flex items-center gap-1 font-semibold text-blackTwo"
          >
            <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
            Agent Suite
          </Link>

          {/* Suite Header */}
          <div className="mb-6 font-spartan">
            <div
              className="h-48 rounded-lg bg-cover bg-center"
              style={{
                backgroundImage: `url(${suite.avatar || suiteFallbackImage})`,
              }}
            >
              <div className="flex h-full items-start justify-between p-6">
                <button className="rounded-md bg-[#F4F4F4] px-6 py-3">
                  <h1 className="text-3xl font-bold text-blackOne">
                    {suite.agentSuiteName}
                  </h1>
                </button>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="mt-6">
                <h2 className="text-2xl font-semibold text-blackOne">
                  {suite.description}
                </h2>
                <p className="font-inter text-lg text-subText">
                  {suite.roleDescription}
                </p>
              </div>
              {!isAgentSuiteClaimed && (
                <button
                  className="rounded-lg bg-primary px-6 py-3 font-normal text-white transition-colors hover:bg-orange-15"
                  onClick={() => handleClaimAgentSuite(suite.agentSuiteKey)}
                  disabled={isLoading}
                >
                  <div className="flex items-center gap-2">
                    {isLoading && (
                      <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                    )}
                    <span>Get Started</span>
                  </div>
                </button>
              )}
            </div>
          </div>

          {/* Agents Grid */}
          {suiteAgents.length > 0 && (
            <div className="grid w-fit grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {suiteAgents.map(agent => (
                <AgentCard
                  key={agent.agentKey}
                  agent={agent}
                  showChatButton={true}
                  link={
                    agent.agentKey
                      ? ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(agent.agentKey)
                      : '#'
                  }
                  onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentSuiteDetailPage;
