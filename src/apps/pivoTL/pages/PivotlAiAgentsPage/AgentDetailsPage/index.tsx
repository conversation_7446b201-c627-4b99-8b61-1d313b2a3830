import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import { useRef, useState } from 'react';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useChatApi } from '@/apps/pivoTL/hooks/useChatApi';
import { ConversationalChatInterface } from '@/apps/pivoTL/components/chat/ConversationalChatInterface';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import {
  featureIcons,
  agentSuites as mockAgents,
} from '@/apps/pivoTL/data/constants';
import { useGetAIAgentsData } from '@/apps/pivoTL/hooks/useAIAgents';
import { useActiveTenant } from '@/apps/pivoTL/context/ActiveTenantContext';
import AgentSuiteSkeletonLoader from '@/apps/pivoTL/components/ui/AgentSuiteSkeleton';
import { AgentCard } from '..';
import { setIq, vesa } from '@/apps/pivoTL/assets/images';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';

const AgentDetailsPage = () => {
  const { state, sendMessage } = useChatApi();
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites } =
    useGetAIAgentsData();
  const { activeAgent: tenantActiveAgent, setActiveAgent } = useActiveTenant();

  const { agentId: agentSuiteId } = useParams<{ agentId: string }>();
  const agentSuite = agentSuiteId
    ? agentSuites.find(agentSuite => agentSuite.agentSuiteKey === agentSuiteId)
    : null;
  const activeAgent = agents.find(
    agent => agent.agentKey === tenantActiveAgent,
  ) || {
    agentName: 'Colton',
    agentKey: 'colton',
    description: 'Collections Coordination & Manager',
    roleDescription:
      'I ensure operations adhere to legal, ethical, and brand standards',
    avatar: vesa,
    roles: [
      'Provide guidance on regulatory matters',
      'Monitor all agent and user interactions',
      'Flag compliance risks',
    ],
    categories: ['COLLECTION_SERVICES'],
  };

  const [showAgentFeatures, setShowAgentFeatures] = useState(false);
  const agentFeaturesRef = useRef<HTMLDivElement>(null);
  const toggleAgentFeatures = () => setShowAgentFeatures(previous => !previous);

  useOnClickOutside(agentFeaturesRef, () => setShowAgentFeatures(false));

  if (!agentSuite) {
    if (isLoadingSuites) {
      return <MainLoaderSkeleton />;
    } else {
      return (
        <div className="-mt-20 flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-blackOne">
              Agent Not Found
            </h1>
            <p className="mb-6 text-gray-600">
              The agent you're looking for doesn't exist.
            </p>
            <Link
              to={ROUTES.PIVOTL_HOME}
              className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
            >
              Back to Home
            </Link>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen">
      <div className="mx-auto max-w-7xl p-4 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* --- Main Content --- */}
          <div className="lg:col-span-2">
            {/* Agent Header */}
            <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: agentSuite.avatar
                    ? `url(${agentSuite.avatar})`
                    : `url(${setIq})`,
                }}
              />
              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-black/10" />
              {/* Content */}
              <div className="relative z-10 flex h-full flex-col justify-center">
                <h1 className="ml-6 w-fit rounded bg-white px-4 py-2 text-[32px] font-bold backdrop-blur-sm">
                  {agentSuite.agentSuiteName}
                </h1>

                <h2 className="ml-6 mt-4 w-fit rounded text-[20px] font-semibold text-white">
                  {agentSuite.description}
                </h2>
                <div className="ml-6 font-inter text-lg text-white">
                  {agentSuite.roleDescription}
                </div>
              </div>
            </div>

            {/* Active agent */}
            {state.messages && state.messages.length === 0 && (
              <div className="relative mt-4 w-[90%]">
                <div
                  ref={agentFeaturesRef}
                  className="mb-4 flex cursor-pointer items-center gap-4 rounded-lg"
                  onClick={toggleAgentFeatures}
                >
                  <div className="h-12 rounded-full bg-peachTwo">
                    <img
                      key={activeAgent.agentKey}
                      src={activeAgent.avatar}
                      className="h-[95%] object-cover"
                      alt={activeAgent.agentName}
                      onError={e => {
                        const fallbackAgent = mockAgents.find(
                          agent =>
                            agent.id.toLowerCase() ===
                            activeAgent.agentKey.toLowerCase(),
                        );
                        if (fallbackAgent) {
                          (e.target as HTMLImageElement).src =
                            fallbackAgent.image;
                        }
                      }}
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <p className="text-xl font-semibold text-darkGray">
                      {activeAgent.agentName}
                    </p>
                    <ChevronRight
                      className={`${
                        showAgentFeatures ? 'rotate-90' : 'rotate-0'
                      }`}
                    />
                  </div>
                </div>

                {/* Marketplace agent features */}
                {showAgentFeatures && (
                  <div className="mb-4 flex h-[120px] w-full items-center gap-4 rounded-lg border border-peachTwo">
                    <div className="h-full rounded-l-lg bg-peachTwo">
                      <img
                        src={activeAgent.avatar}
                        className="h-[95%] object-cover"
                        alt={activeAgent.agentName}
                        onError={e => {
                          const fallbackAgent = mockAgents.find(
                            agent =>
                              agent.id.toLowerCase() ===
                              activeAgent.agentKey.toLowerCase(),
                          );
                          if (fallbackAgent) {
                            (e.target as HTMLImageElement).src =
                              fallbackAgent.image;
                          }
                        }}
                      />
                    </div>
                    <div className="flex flex-col gap-4">
                      {activeAgent.roles.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <img
                            src={featureIcons[index % 3]}
                            alt=""
                            className="w-5"
                          />
                          <p className="text-sm text-blackTwo">{feature}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="bg-white font-inter">
              <ConversationalChatInterface
                state={state}
                sendMessage={sendMessage}
              />
            </div>
          </div>

          {/* --- Sidebar --- */}
          <div className="lg:col-span-1">
            {isLoadingAgents ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <div className="flex flex-col gap-4">
                {agents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    className="w-full max-w-[334px]"
                    agent={agent}
                    showChatButton
                    isActiveAgent={activeAgent.agentKey === agent.agentKey}
                    link={ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(
                      agent.agentKey,
                    )}
                    onAgentSelect={() => setActiveAgent(agent.agentKey)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDetailsPage;
