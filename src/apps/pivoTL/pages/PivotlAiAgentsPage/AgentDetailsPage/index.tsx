import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
// import { useState } from 'react';
import { useChatApi } from '@/apps/pivoTL/hooks/useChatApi';
import { ConversationalChatInterface } from '@/apps/pivoTL/components/chat/ConversationalChatInterface';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
// import { marketplaceAgents } from '@/apps/pivoTL/data/constants';
import { useGetAIAgentsData } from '@/apps/pivoTL/hooks/useAIAgents';
import { useActiveTenant } from '@/apps/pivoTL/context/ActiveTenantContext';
import AgentSuiteSkeletonLoader from '@/apps/pivoTL/components/ui/AgentSuiteSkeleton';
import { AgentCard } from '..';
import { vesa } from '@/apps/pivoTL/assets/images';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';

const AgentDetailsPage = () => {
  const { state, sendMessage } = useChatApi();
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites } =
    useGetAIAgentsData();
  const { activeAgent: tenantActiveAgent, setActiveAgent } = useActiveTenant();

  const { agentId: agentSuiteId } = useParams<{ agentId: string }>();
  const agentSuite = agentSuiteId
    ? agentSuites.find(agentSuite => agentSuite.agentSuiteKey === agentSuiteId)
    : null;
  const activeAgent = agents.find(
    agent => agent.agentKey === tenantActiveAgent,
  ) || {
    agentName: 'Colton',
    agentKey: 'colton',
    description: 'Collections Coordination & Manager',
    roleDescription:
      'I ensure operations adhere to legal, ethical, and brand standards',
    avatar: vesa,
    roles: [
      'Provide guidance on regulatory matters',
      'Monitor all agent and user interactions',
      'Flag compliance risks',
    ],
    categories: ['COLLECTION_SERVICES'],
  };

  if (!agentSuite) {
    if (isLoadingSuites) {
      return <MainLoaderSkeleton />;
    } else {
      return (
        <div className="-mt-20 flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-blackOne">
              Agent Not Found
            </h1>
            <p className="mb-6 text-gray-600">
              The agent you're looking for doesn't exist.
            </p>
            <Link
              to={ROUTES.PIVOTL_HOME}
              className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
            >
              Back to Home
            </Link>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen">
      <div className="mx-auto max-w-7xl p-4 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* --- Main Content --- */}
          <div className="lg:col-span-2">
            {/* Agent Header */}
            <div>
              <div
                className="h-[168px] rounded-t-lg border bg-gray-200 bg-cover bg-center"
                style={{ backgroundImage: `url(${agentSuite.avatar})` }}
              >
                <h1 className="ml-6 mt-6 w-fit rounded px-4 py-2 text-[32px] font-bold text-white">
                  {agentSuite.agentSuiteName}
                </h1>
              </div>
              <div className="py-4">
                <h2 className="mb-1.5 text-2xl font-semibold">
                  {agentSuite.description}
                </h2>
                <p className="font-inter text-lg">
                  {agentSuite.roleDescription}
                </p>
              </div>
            </div>

            {/* Active Marketplace agent */}
            {state.messages && state.messages.length === 0 && (
              <div className="relative w-[90%]">
                <div className="mb-4 flex cursor-pointer items-center gap-4 rounded-lg">
                  <div className="h-12 rounded-full bg-peachTwo">
                    <img
                      src={activeAgent.avatar}
                      className="h-[95%] object-cover"
                      alt={activeAgent.agentName}
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <p className="text-xl font-semibold text-darkGray">
                      {activeAgent.agentName}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div
              className={`${state.messages && state.messages.length === 0 ? 'h-2/3' : 'h-[600px]'} bg-white font-inter`}
            >
              <ConversationalChatInterface
                state={state}
                sendMessage={sendMessage}
              />
            </div>
          </div>

          {/* --- Sidebar --- */}
          <div className="lg:col-span-1">
            {/* <MarketplaceAgentsSidebar
              activeAgent={activeAgent}
              onAgentSelect={setActiveAgent}
            /> */}

            {isLoadingAgents ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <div className="flex flex-col gap-4">
                {agents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    className="w-full max-w-[334px]"
                    agent={agent}
                    showChatButton
                    isActiveAgent={activeAgent.agentKey === agent.agentKey}
                    link={ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(
                      agent.agentKey,
                    )}
                    onAgentSelect={() => setActiveAgent(agent.agentKey)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDetailsPage;
