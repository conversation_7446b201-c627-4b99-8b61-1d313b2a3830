import { Link } from 'react-router-dom';
import { arrowPrimary, chatIcon, paperPlane } from '../../assets/icons';
import { fullEclipse, regis } from '../../assets/images';
import { ROUTES } from '../../constants/routes';
import {
  PivotlAuthProvider,
  usePivotlAuth,
} from '../../context/PivotlAuthContext';

const AgentPill = ({ name, icon = regis }: { name: string; icon?: string }) => (
  <div className="flex w-fit items-center gap-2 rounded-lg border border-primary p-1.5">
    <div className="h-6 w-6 flex-shrink-0">
      <div className="rounded-full bg-[#718EBF33]">
        <img
          src={icon}
          alt={name}
          className="h-full w-full rounded-full object-cover"
        />
      </div>
    </div>
    <span className="font-medium">{name}</span>
    <img src={chatIcon} alt="chat icon" />
  </div>
);

const AgentFlowIndicator = () => (
  <div className="mt-6 flex items-center justify-between">
    <AgentPill name="Liora" />
    <img src={arrowPrimary} alt="arrow" />
    <AgentPill name="Seto" />
    <img src={arrowPrimary} alt="arrow" />
    <AgentPill name="Risa" />
  </div>
);
import { TenantProvider } from '../../context/TenantContext';

const HeroSectionContent = () => {
  const { isAuthenticated } = usePivotlAuth();

  return (
    <section className="relative overflow-hidden">
      {/* Background Ball */}
      <div
        className="pointer-events-none absolute right-0 top-0 z-0 mt-12 h-16 w-16 md:h-[108px] md:w-[108px]"
        style={{
          backgroundImage: `url(${fullEclipse})`,
          backgroundSize: 'contain',
          backgroundPosition: 'right top',
          backgroundRepeat: 'no-repeat',
          transform: 'translate(15%, -30%)',
        }}
      />

      <div className="relative z-10 mx-auto flex max-w-screen-2xl flex-col items-center px-4 py-24 sm:px-6 md:flex-row md:gap-8 lg:px-8">
        {/* LHS - Content */}
        <div className="md:min-w-1/2 w-full text-center md:w-1/2 md:text-left">
          <h1 className="mb-6 text-4xl font-semibold leading-normal text-gray-900 md:text-[56px]">
            The Agentic AI
            <br />
            Transformation Hub
          </h1>
          <p className="mx-auto mb-8 max-w-[627px] font-inter text-lg text-gray-700">
            Orchestrate, evolve, and deploy AI agents across your enterprise.
            <br />
            Agentous PivoTL is the transformation layer that deploys autonomous
            agentic teams to manage critical business functions and accelerate
            value creation.
          </p>
          <div className="flex flex-col justify-center gap-4 px-4 sm:flex-row md:justify-start md:px-0">
            <Link
              to={
                isAuthenticated ? ROUTES.DASHBOARD_BASE : ROUTES.PIVOTL_SIGNUP
              }
              className="rounded-md bg-primary px-8 py-[9px] font-semibold text-white transition-colors hover:bg-orange-15"
            >
              {isAuthenticated ? 'Go to Dashboard' : 'Chat with Regis'}
            </Link>
          </div>
        </div>

        {/* RHS - Interactive agents interface */}
        <div className="w-full md:w-1/2">
          <div className="flex items-center justify-center pt-10 font-inter md:pt-0">
            <div className="w-[438px] min-w-fit">
              <div className="rounded-2xl bg-white p-4 shadow-md">
                <div className="mb-6 flex gap-3">
                  <div className="h-12 w-12 flex-shrink-0">
                    {/* Selected agent's image */}
                    <div className="rounded-full bg-grayTwentySix">
                      <img
                        src={regis}
                        alt="Regis"
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                  </div>

                  <div className="flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      {/* Selected agent's name */}
                      <span className="font-semibold text-darkGray">Regis</span>
                    </div>
                    <div className="rounded-lg bg-gray-5 p-3 font-medium text-grayTwentyFour">
                      <div className="typing w-fit">
                        {/* Selected agent's description */}
                        <span>Hi, I am Regis ready to get started!</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between rounded-md border border-grayNine px-4 py-2 text-grayTen">
                  <span>I'm here — whenever you're ready.</span>
                  <img src={paperPlane} alt="send message" />
                </div>
              </div>

              {/* Agents carousel */}
              <AgentFlowIndicator />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export const HeroSection = () => {
  return (
    <TenantProvider>
      <PivotlAuthProvider>
        <HeroSectionContent />
      </PivotlAuthProvider>
    </TenantProvider>
  );
};
