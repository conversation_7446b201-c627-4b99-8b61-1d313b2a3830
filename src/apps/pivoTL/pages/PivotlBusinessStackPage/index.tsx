import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { AvailableAppsGrid } from '../../components/businessStack/AvailableAppsGrid';
import { useAvailableAppsApi } from '../../services/businessStackService';
import { useConnectionFlow } from '../../hooks/useConnectionFlow';
import { useActiveTenant } from '../../context/ActiveTenantContext';
import { BusinessStackPageState } from '../../types/businessStack';
import AppContainer from '../../components/common/AppContainer';
import { Icons } from '../../assets/icons/DashboardIcons';
import EnhancedChatSidebar from '../../components/common/EnhancedChatSidebar';
import AgentsDropdown from '../../components/ui/AgentsDropdown';

const BusinessStackPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams<{ appKey?: string }>();
  const [pageState, setPageState] = useState<BusinessStackPageState>({
    availableApps: [],
    isLoadingApps: true,
    searchQuery: '',
    selectedCategory: '',
    scyraChatState: {
      messages: [],
      isLoading: false,
      sessionId: '',
    },
    currentPage: 1,
    totalPages: 1,
  });
  const [isProcessingOAuth, setIsProcessingOAuth] = useState(false);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const oauthCallbackProcessedRef = useRef<string | null>(null); // Track processed OAuth callbacks

  // Initialize connection flow
  const connectionFlow = useConnectionFlow((app, isConnected) => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(existingApp =>
        existingApp.key === app.key
          ? { ...existingApp, isConnected }
          : existingApp,
      ),
    }));
  });

  const getAvailableApps = useAvailableAppsApi();
  const { activeAgent, isActiveTenantLoading } = useActiveTenant();

  // Trigger reload of available apps and reset connection flow when agent changes
  useEffect(() => {
    if (isActiveTenantLoading || !activeAgent) return;

    const reloadForAgent = async () => {
      try {
        setPageState(prev => ({ ...prev, isLoadingApps: true }));

        // Reload chat history for the new agent
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }

        // Use current page state values for consistency
        const response = await getAvailableApps({
          page: pageState.currentPage,
          size: 10,
          search: pageState.searchQuery,
          appCategory: pageState.selectedCategory,
        });

        setPageState(prev => ({
          ...prev,
          availableApps: response.data.availableApps,
          isLoadingApps: false,
          totalPages: response.data.total,
        }));

        // Reset connection flow state completely (don't preserve messages from other agents)
        connectionFlow.resetFlow(false);
      } catch (error) {
        console.error('Error reloading apps for agent change:', error);
        setPageState(prev => ({ ...prev, isLoadingApps: false }));
      }
    };

    reloadForAgent();
  }, [activeAgent, isActiveTenantLoading]);

  // Handle OAuth callback parameters on component mount
  useEffect(() => {
    const handleOAuthCallback = async () => {
      const urlParams = new URLSearchParams(location.search);
      const appKey = params.appKey; // Get appKey from path parameter
      const code = urlParams.get('code');
      const state = urlParams.get('state');

      // Only process if we have both appKey and code (OAuth callback scenario)
      if (appKey && code) {
        // Create unique callback identifier to prevent duplicate processing
        const callbackId = `${appKey}-${code}-${state}`;

        // Check if this callback has already been processed
        if (oauthCallbackProcessedRef.current === callbackId) {
          // console.log(
          //   'OAuth callback already processed, skipping:',
          //   callbackId,
          // );
          return;
        }

        // Check if already processing OAuth to prevent concurrent processing
        if (isProcessingOAuth) {
          // console.log('OAuth callback already in progress, skipping');
          return;
        }

        // console.log('Processing OAuth callback for:', callbackId);
        oauthCallbackProcessedRef.current = callbackId;
        setIsProcessingOAuth(true);

        // Small delay to ensure all state updates are complete
        await new Promise(resolve => setTimeout(resolve, 100));

        try {
          // Find the app by key to set it as current app for the connection flow
          const response = await getAvailableApps({
            page: 1,
            size: 100, // Get all apps to find the one we need
            search: '',
            appCategory: '',
          });

          const targetApp = response.data.availableApps.find(
            app => app.key === appKey,
          );

          if (targetApp) {
            // console.log('Found target app for OAuth callback:', targetApp.name);
            // Process the OAuth callback with the full app object
            // DO NOT call startConnection here as it would trigger another OAuth flow
            await connectionFlow.handleOAuthCallbackWithApp(
              targetApp,
              code,
              state || undefined,
            );
            // console.log(
            //   'OAuth callback processing completed for:',
            //   targetApp.name,
            // );
          } else {
            console.error('App not found for key:', appKey);
            throw new Error(`App not found for key: ${appKey}`);
          }
        } catch (error) {
          console.error('Error processing OAuth callback:', error);
          // Reset the processed flag on error so it can be retried
          oauthCallbackProcessedRef.current = null;
        } finally {
          setIsProcessingOAuth(false);

          // Clean up URL - navigate back to business-stack without appKey
          navigate('/pivotl/dashboard/business-stack', { replace: true });
        }
      } // Close the if (appKey && code) block
    };

    handleOAuthCallback();
  }, [location.search, params.appKey, isProcessingOAuth]); // Include isProcessingOAuth to prevent concurrent processing

  // Load available apps on component mount
  useEffect(() => {
    const loadAvailableApps = async () => {
      try {
        setPageState(prev => ({ ...prev, isLoadingApps: true }));
        const response = await getAvailableApps({
          page: pageState.currentPage,
          size: 10,
          search: pageState.searchQuery,
          appCategory: pageState.selectedCategory,
        });

        setPageState(prev => ({
          ...prev,
          availableApps: response.data.availableApps,
          isLoadingApps: false,
          totalPages: response.data.total,
        }));
      } catch (error) {
        console.error('Error loading available apps:', error);
        setPageState(prev => ({ ...prev, isLoadingApps: false }));
      }
    };

    loadAvailableApps();
  }, [
    pageState.currentPage,
    pageState.searchQuery,
    pageState.selectedCategory,
  ]);

  // Handle search query changes
  const handleSearchChange = (query: string) => {
    setPageState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));
  };

  // Handle category changes
  const handleCategoryChange = (category: string) => {
    setPageState(prev => ({
      ...prev,
      selectedCategory: category,
      currentPage: 1, // Reset to first page when filtering
    }));
  };

  // Handle app connection (for non-modal apps)
  const handleConnectApp = async (appName: string) => {
    // This will be handled by the connection flow now
    console.log('Connecting to app:', appName);
  };

  // Show loading state while processing OAuth callback
  if (isProcessingOAuth) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-blackFour">Processing authentication...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex flex-1 overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
          {/* LHS - Chat Interface */}
          <EnhancedChatSidebar
            connectionFlow={connectionFlow}
            reloadChatHistoryRef={reloadChatHistoryRef}
          />

          {/* RHS - Available Apps Grid */}
          <div className="flex-1 overflow-y-auto">
            <AppContainer className="space-y-6 p-8 lg:space-y-8">
              <div className="lg:max-w-[800px]">
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center text-primary">
                        <Icons.Stack className="h-6 w-6" />
                      </div>
                      <div className="text-blackFour">
                        <h1 className="text-xl font-semibold lg:text-2xl">
                          Business Stack
                        </h1>
                        <p>Connect Each Agent</p>
                      </div>
                    </div>

                    {/* AgentsDropdown Component */}
                    <AgentsDropdown />
                  </div>
                </div>

                <div className="flex-1 overflow-hidden">
                  <AvailableAppsGrid
                    apps={pageState.availableApps}
                    isLoading={pageState.isLoadingApps}
                    searchQuery={pageState.searchQuery}
                    selectedCategory={pageState.selectedCategory}
                    onSearchChange={handleSearchChange}
                    onCategoryChange={handleCategoryChange}
                    onConnectApp={handleConnectApp}
                    connectionFlow={connectionFlow}
                  />
                </div>
              </div>
            </AppContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessStackPage;
