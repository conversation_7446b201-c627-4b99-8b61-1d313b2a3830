import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { LogOut, Settings, Check } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { usePivotlAuth } from '../../context/PivotlAuthContext';
import { useTenant } from '../../context/TenantContext';
import { ROUTES } from '../../constants/routes';
import UserAvatar from './UserAvatar';
import { Icons } from '../../assets/icons/DashboardIcons';

interface UserDropdownProps {
  className?: string;
}

export const UserDropdown: React.FC<UserDropdownProps> = ({
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [buttonRect, setButtonRect] = useState<DOMRect | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  const { user, logout } = usePivotlAuth();
  const { selectedTenant } = useTenant();
  const navigate = useNavigate();

  const userName = user
    ? `${user.firstName || ''} ${user.lastName || ''}`.trim()
    : 'User';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleProfileClick = () => {
    setIsOpen(false);
    navigate(ROUTES.DASHBOARD_SETTINGS);
  };

  const handleLogout = () => {
    setIsOpen(false);
    logout();
  };

  const toggleDropdown = () => {
    if (!isOpen && buttonRef.current) {
      setButtonRect(buttonRef.current.getBoundingClientRect());
    }
    setIsOpen(!isOpen);
  };

  return (
    <>
      <div className={`relative ${className}`}>
        {/* Avatar Button */}
        <div ref={buttonRef} onClick={toggleDropdown}>
          <UserAvatar
            fullName={userName}
            profileImage={user?.profilePicture || undefined}
            size="md"
            isLabel={false}
            border={true}
            useRandomColor={false}
            defaultColor="#121212"
            borderColor="#FF3E00"
            textColor="#fff"
            borderWidth={1.5}
          />
        </div>
      </div>

      {/* Dropdown Menu - Rendered via Portal */}
      {isOpen &&
        buttonRect &&
        createPortal(
          <AnimatePresence>
            <motion.div
              ref={dropdownRef}
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.15, ease: 'easeOut' }}
              className="fixed z-[9999] w-[230px] overflow-hidden rounded-xl border border-gray-200 bg-white shadow-[0_0_10px_rgba(0,0,0,0.1)]"
              style={{
                top: buttonRect.bottom + 8,
                right: window.innerWidth - buttonRect.right,
              }}
            >
              {/* User Info Section */}
              <div className=" border-b border-[#8890A1] bg-[#e8e7e4] p-3">
                <div className="flex w-full items-center space-x-3">
                  <UserAvatar
                    fullName={userName}
                    profileImage={user?.profilePicture || undefined}
                    size="md"
                    isLabel={false}
                    labelComponent={
                      <div className="min-w-0 max-w-[100px] flex-1">
                        <p className="truncate text-sm font-bold text-blackOne">
                          {userName}
                        </p>
                        <p className="truncate text-xs text-blackOne">
                          {/* {selectedTenant?.name || ''} */}
                        </p>
                      </div>
                    }
                    className="rounded-full"
                  />

                  {selectedTenant && (
                    <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-gray-300">
                      <Check
                        className="h-4 w-4 text-blackOne"
                        strokeWidth={4}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Menu Items */}
              <div className="py-2">
                <button
                  onClick={() => {
                    navigate(ROUTES.DASHBOARD_BASE);
                  }}
                  className="flex w-full items-center px-4 py-3 text-sm text-orange-600 transition-colors hover:bg-orange-50"
                >
                  <Icons.Dashboard className="mr-3 h-4 w-4 text-orange-600" />
                  Dashboard
                </button>
                <button
                  onClick={handleProfileClick}
                  className="flex w-full items-center px-4 py-3 text-sm text-orange-600 transition-colors hover:bg-orange-50"
                >
                  <Settings className="mr-3 h-4 w-4 text-orange-600" />
                  Settings
                </button>

                <button
                  onClick={handleLogout}
                  className="flex w-full items-center px-4 py-3 text-sm text-blackOne transition-colors hover:bg-gray-50"
                >
                  <LogOut className="mr-3 h-4 w-4 text-blackOne" />
                  Log out
                </button>
              </div>
            </motion.div>
          </AnimatePresence>,
          document.body,
        )}
    </>
  );
};
