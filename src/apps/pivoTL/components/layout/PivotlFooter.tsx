/*
import { Link } from 'react-router-dom';
import Pivo<PERSON><PERSON>ogo from '../ui/PivotlLogo';

type FooterLink = {
  to: string;
  text: string;
};

type FooterSection = {
  title: string;
  links: FooterLink[];
};

const footerLinks: FooterSection[] = [
  {
    title: 'Features',
    links: [
      { to: '/pivotl/statements', text: 'Statements' },
      { to: '/pivotl/grants', text: 'Grants' },
      { to: '/pivotl/learn', text: 'Learn' },
      { to: '/pivotl/pricing', text: 'Pricing' },
    ],
  },
  {
    title: 'Company',
    links: [
      { to: '/pivotl/about', text: 'About us' },
      { to: '/pivotl/careers', text: 'Careers' },
      { to: '/pivotl/press', text: 'Press' },
      { to: '/pivotl/event', text: 'Event' },
    ],
  },
  {
    title: 'Legal',
    links: [
      { to: '/pivotl/terms', text: 'Terms' },
      { to: '/pivotl/privacy', text: 'Privacy' },
      { to: '/pivotl/cookies', text: 'Cookies' },
      { to: '/pivotl/contact', text: 'Contact' },
    ],
  },
];

const FooterLinkItem = ({ to, text }: FooterLink) => (
  <li>
    <Link
      to={to}
      className="flex items-center gap-2 font-medium transition-colors hover:text-gray-400"
    >
      {text}
      {text === 'Pricing' && (
        <span className="rounded-full bg-whiteOff px-2 py-1 text-xs font-medium text-black">
          New
        </span>
      )}
    </Link>
  </li>
);

const FooterSection = ({ title, links }: FooterSection) => (
  <div className="text-center md:text-left">
    <h3 className="mb-4 text-lg font-semibold text-gray-400">{title}</h3>
    <ul className="flex flex-col items-center space-y-2 md:items-start">
      {links.map(link => (
        <FooterLinkItem key={link.to} {...link} />
      ))}
    </ul>
  </div>
);
*/

export const PivotlFooter = () => {
  return (
    <footer className="text-white">
      <div className="mx-auto flex max-w-screen-3xl justify-center bg-darkBlueOne">
        {/* <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
          {/* Logo and Description - Centered on mobile *}
          <div className="col-span-1 text-center md:col-span-2 md:text-left">
            <div className="flex justify-center md:block">
              <PivotlLogo variant="light" />
            </div>
            <p className="mx-auto max-w-md py-4 text-lg font-medium md:mx-0">
              The AI Agent Marketplace
              <br />
              and Transformation Layer
            </p>
          </div>

          {/* Footer Links - Centered on mobile *}
          {footerLinks.map(section => (
            <FooterSection key={section.title} {...section} />
          ))}
        </div> */}

        {/* Bottom text - Centered on mobile */}
        <div className="flex w-full max-w-screen-2xl flex-col items-center px-4 py-8 text-grayTwentyFive sm:px-6 md:flex-row md:gap-10">
          <p className="text-center text-sm md:text-left">
            © 2025 by Agentous Inc.
          </p>
          <p className="mt-4 text-center text-sm md:mt-0">
            All rights are reserved. All trademarks, service marks, and logos
            used on this site are the property of Agentous Inc.
          </p>
        </div>
      </div>
    </footer>
  );
};
