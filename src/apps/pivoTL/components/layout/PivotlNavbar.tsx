import { Link, useLocation } from 'react-router-dom';
import { useState } from 'react';
import PivotlLogo from '../ui/PivotlLogo';
import { ROUTES } from '../../constants/routes';
import {
  PivotlAuthProvider,
  usePivotlAuth,
} from '../../context/PivotlAuthContext';
import { TenantProvider } from '../../context/TenantContext';
import { UserDropdown } from '../ui/UserDropdown';

const PivotlNavbarContent = () => {
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isAuthenticated, login } = usePivotlAuth();

  const navItems = [
    { path: ROUTES.PIVOTL_PRODUCTS, label: 'Products' },
    { path: ROUTES.PIVOTL_ABOUT, label: 'About Us' },
    { path: ROUTES.PIVOTL_PRICING, label: 'Pricing', exact: true },
  ];

  const isActive = (path: string, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  const handleLoginClick = () => {
    login();
  };

  return (
    <nav className="z-10 bg-white">
      <div className="mx-auto max-w-screen-2xl px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between py-6">
          <div className="flex items-center gap-24">
            <PivotlLogo />

            {/* Desktop Navigation */}
            <div className="hidden items-center space-x-4 md:flex">
              {navItems.map(item => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                    isActive(item.path, item.exact)
                      ? 'bg-orange-50 text-orange-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-orange-600'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Desktop Signin Buttons / User Menu */}
          <div className="hidden items-center space-x-4 md:flex">
            {isAuthenticated ? (
              <UserDropdown />
            ) : (
              <>
                <button
                  onClick={handleLoginClick}
                  className="rounded-md border border-gray-700 px-6 py-2 font-medium text-gray-700 transition-colors hover:border-primary hover:bg-orange-50 hover:text-primary"
                >
                  Login
                </button>
                <Link
                  to={ROUTES.PIVOTL_SIGNUP}
                  className="rounded-lg bg-primary px-8 py-[9px] font-semibold text-white transition-colors hover:bg-orange-15"
                >
                  Create Account
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-orange-600 focus:text-orange-600 focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2 sm:px-3">
              {navItems.map(item => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMenuOpen(false)}
                  className={`block rounded-md px-3 py-2 text-base font-medium transition-colors ${
                    isActive(item.path, item.exact)
                      ? 'bg-orange-50 text-orange-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-orange-600'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
              {isAuthenticated ? (
                <>
                  <Link
                    to={ROUTES.DASHBOARD_BASE}
                    onClick={() => setIsMenuOpen(false)}
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-orange-600"
                  >
                    Dashboard
                  </Link>
                  <div className="px-3 py-2">
                    <UserDropdown />
                  </div>
                </>
              ) : (
                <>
                  <button
                    onClick={() => {
                      handleLoginClick();
                      setIsMenuOpen(false);
                    }}
                    className="block w-full rounded-md px-3 py-2 text-left text-base font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-orange-600"
                  >
                    Login
                  </button>
                  <Link
                    to={ROUTES.PIVOTL_SIGNUP}
                    onClick={() => setIsMenuOpen(false)}
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-orange-600"
                  >
                    Create Account
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export const PivotlNavbar = () => {
  return (
    <TenantProvider>
      <PivotlAuthProvider>
        <PivotlNavbarContent />
      </PivotlAuthProvider>
    </TenantProvider>
  );
};
