import React, { useRef } from 'react';
import EnhancedChatSidebar from '../common/EnhancedChatSidebar';
import { useGetAIAgentSuites } from '../../hooks/useAIAgents';
import AgentSuiteSkeletonLoader from '../ui/AgentSuiteSkeleton';
import { AgentSuiteCard } from '../../pages/PivotlAiAgentsPage';
import { AIAgentSuite } from '../../services/upivotalAgenticService';
import { Icons } from '../../assets/icons/DashboardIcons';

interface AgentSelectionLayoutProps {
  title: string;
  description: string;
  bgImage: string;
  pageType: 'knowledge-base' | 'business-stack';
  onAgentSuiteClick: (suite: AIAgentSuite) => void;
}

const AgentSelectionLayout: React.FC<AgentSelectionLayoutProps> = ({
  title,
  description,
  bgImage,
  pageType,
  onAgentSuiteClick,
}) => {
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const { data: agentSuites = [], isLoading, error } = useGetAIAgentSuites();

  // Handle agent suite card click
  const handleAgentSuiteClick = (suite: any) => {
    onAgentSuiteClick(suite);
  };

  return (
    <div className="flex h-full">
      {/* Chat Interface - LHS */}
      <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />

      {/* Main Content - RHS */}
      <div className="flex flex-1 flex-col gap-6 overflow-y-auto p-8">
        <div className="flex flex-col gap-4 text-start">
          <div className="mb-2 flex items-center gap-2">
            {pageType === 'knowledge-base' && (
              <Icons.Knowledge className="h-6 w-6 text-primary" />
            )}
            {pageType === 'business-stack' && (
              <Icons.Stack className="h-6 w-6 text-primary" />
            )}
            <h1 className="text-2xl font-semibold text-blackOne">
              {pageType === 'knowledge-base'
                ? 'Knowledge Base'
                : 'Business Stack'}
            </h1>
          </div>

          {/* Hero Section */}
          <div
            className="flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-transparent bg-cover bg-center text-white"
            style={{ backgroundImage: `url(${bgImage})` }}
          >
            <div className="flex w-full items-center justify-between">
              <div className="p-6 text-left">
                <h2 className="mb-2 text-lg font-bold text-white">{title}</h2>
                <p className="text-sm text-white">{description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Activate an agent to begin section */}
        <h3 className="text-lg font-medium text-blackOne">
          {pageType === 'knowledge-base'
            ? 'Select Suite -> Upload Knowledge Base'
            : 'Select Suite -> Connect Your Apps'}
        </h3>
        {/* Error State */}
        {error && (
          <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-700">
            <p>Error loading agent suites: {error.message}</p>
          </div>
        )}

        {/* Content */}
        <div className="w-fit">
          {isLoading ? (
            <AgentSuiteSkeletonLoader count={4} />
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {agentSuites.map(suite => (
                <AgentSuiteCard
                  key={suite.agentSuiteKey}
                  suite={suite}
                  link={'#'}
                  onAgentSuiteClick={() => handleAgentSuiteClick(suite)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentSelectionLayout;
