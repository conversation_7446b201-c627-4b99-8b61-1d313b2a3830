import { useEffect, useRef, useState } from 'react';
import { RegisState } from '../../hooks/useRegisChat';
import { User } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { regis } from '../../assets/images';
import moment from 'moment';
import { TypingIndicator } from './TypingIndicator';

interface RegisChatInterfaceProps {
  state: RegisState;
  ChatInputComponent: React.ComponentType;
  isStreaming?: boolean;
  streamingMessage?: string;
}

const RegisMessage = ({
  message,
  shouldAnimate,
}: {
  message: any;
  shouldAnimate?: boolean;
}) => {
  const isUser = message.sender === 'user';

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-peachTwo">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={regis}
              alt="Regis"
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format('h:mm A')}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-3xl bg-gray-5 p-3 text-grayTwentyFour">
          <StreamingText
            content={message.content}
            shouldAnimate={shouldAnimate}
          />
        </div>
      </div>
    </div>
  );
};

const StreamingText = ({
  content,
  shouldAnimate,
}: {
  content: string;
  shouldAnimate?: boolean;
}) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!shouldAnimate) {
      setDisplayedContent(content);
      setIsTyping(false);
      return;
    }

    // If content is being updated while streaming, just show it directly
    if (
      content.length > displayedContent.length &&
      content.startsWith(displayedContent)
    ) {
      setDisplayedContent(content);
      setIsTyping(content.length > 0);
      return;
    }

    // For new content, start the typing animation
    if (content !== displayedContent) {
      setDisplayedContent('');
      setIsTyping(true);

      const animateText = () => {
        let index = 0;

        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }

        intervalRef.current = setInterval(() => {
          if (index <= content.length) {
            setDisplayedContent(content.slice(0, index));
            index++;
          } else {
            setIsTyping(false);
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          }
        }, 50); // Typing speed
      };

      const timer = setTimeout(animateText, 100);
      return () => {
        clearTimeout(timer);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [content, shouldAnimate]);

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <span>
      <ReactMarkdown
        components={{
          p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
          h1: ({ children }) => (
            <h1 className="mb-2 text-lg font-bold">{children}</h1>
          ),
          h2: ({ children }) => (
            <h2 className="mb-2 text-base font-bold">{children}</h2>
          ),
          h3: ({ children }) => (
            <h3 className="mb-2 text-sm font-bold">{children}</h3>
          ),
          ul: ({ children }) => (
            <ul className="mb-2 ml-4 list-disc">{children}</ul>
          ),
          ol: ({ children }) => (
            <ol className="mb-2 ml-4 list-decimal">{children}</ol>
          ),
          li: ({ children }) => <li className="mb-2">{children}</li>,
          code: ({ children }) => (
            <code className="rounded bg-gray-200 px-1 py-0.5 font-mono text-sm">
              {children}
            </code>
          ),
          pre: ({ children }) => (
            <pre className="mb-2 overflow-x-auto rounded bg-gray-200 p-2 font-mono text-sm">
              {children}
            </pre>
          ),
          a: ({ children, href }) => (
            <a
              href={href}
              className="text-primary hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          strong: ({ children }) => (
            <strong className="font-bold">{children}</strong>
          ),
          em: ({ children }) => <em className="italic">{children}</em>,
          // Custom text renderer to handle whitespace
          text: ({ children }) => {
            if (typeof children === 'string') {
              // Remove excessive whitespace but preserve single spaces
              return children.replace(/\s+/g, ' ');
            }
            return children;
          },
        }}
      >
        {displayedContent}
      </ReactMarkdown>
      {shouldAnimate && isTyping && (
        <span className="ml-1 animate-pulse text-blue-500">|</span>
      )}
    </span>
  );
};

export const RegisChatInterface = ({
  state,
  ChatInputComponent,
  isStreaming = false,
  streamingMessage = '',
}: RegisChatInterfaceProps) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  // Track user scroll position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => container.removeEventListener('scroll', handleScroll);
  }, [state.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom or this is the initial load
    if (isUserAtBottom || state.messages.length === 0) {
      scrollToBottom();
    }
  }, [state.messages, isUserAtBottom]);

  // Handle loading and streaming state changes - only scroll if user is at bottom
  useEffect(() => {
    if ((!state.isLoading || isStreaming) && isUserAtBottom) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [state.isLoading, isStreaming, streamingMessage, isUserAtBottom]);

  return (
    <div className="flex h-full flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-4"
        style={{ minHeight: 0 }} // Ensures flex-1 works properly
      >
        {state.messages.map(message => (
          <RegisMessage
            key={message.id}
            message={message}
            shouldAnimate={false}
          />
        ))}

        {/* Streaming Message */}
        {isStreaming && streamingMessage && (
          <div className="mb-6 flex gap-3">
            {/* Avatar */}
            <div className="h-10 w-10 flex-shrink-0">
              <div className="rounded-full bg-grayTwentySix">
                <img
                  src={regis}
                  alt="Regis"
                  className="h-full w-full rounded-full object-cover"
                />
              </div>
            </div>

            {/* Message Content */}
            <div className="flex-1">
              {/* Header */}
              <div className="mb-1 flex items-center gap-2">
                <span className="font-semibold text-darkGray">Regis</span>
                <span className="text-sm text-gray-400">
                  {moment().format('h:mm A')}
                </span>
              </div>

              {/* Streaming Message Text */}
              <div className="rounded-3xl bg-gray-5 p-3 text-grayTwentyFour">
                <StreamingText
                  content={streamingMessage}
                  shouldAnimate={true}
                />
              </div>
            </div>
          </div>
        )}

        {/* Typing Indicator - only show when not streaming */}
        {state.isLoading && !isStreaming && (
          <TypingIndicator agentImageSrc={regis} agentName="Regis" />
        )}
      </div>

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">
        <ChatInputComponent />
      </div>
    </div>
  );
};
