import { cubeIcon, rhombusIcon, starIcon } from '../assets/icons';
import {
  novaIq,
  operatorIq,
  salesWing,
  scyra,
  setIq,
  colin,
  vesa,
  liora,
  seto,
} from '../assets/images';

export interface AgentSuite {
  id: string;
  name: string;
  category: string;
  description: string;
  image: string;
  agents?: string[]; // IDs of agents in this suite
}

export interface IndividualAgent {
  id: string;
  name: string;
  role: string;
  description: string;
  category: string;
  image: string;
  suite?: string; // Suite ID this agent belongs to
  features: string[];
}

export const agentSuites: AgentSuite[] = [
  {
    id: 'set-iq',
    name: 'SetIQ',
    category: 'Collection Services AI Agents Suite',
    description:
      'Resolve accounts with precision, empathy, speed, and compliance.',
    image: setIq,
    agents: ['scyra', 'obed', 'compton', 'colton'],
  },
  {
    name: 'SalesWing',
    id: 'sales-wing',
    category: 'Sales Operations AI Agents Suite',
    description: 'Engage leads, schedule calls, and accelerate deal cycles.',
    image: salesWing,
  },
  {
    name: 'OperatorIQ',
    id: 'operator-iq',
    category: 'Project & Program Oversight AI Agents Suite',
    description:
      'Track progress, manage projects, and deliver results on time.',
    image: operatorIq,
  },
  {
    name: 'NovaIQ',
    id: 'nova-iq',
    category: 'Strategy & Innovation AI Agents Suite',
    description:
      'Drive strategy, spark innovation, and align teams effectively.',
    image: novaIq,
  },
  {
    name: 'Scyra',
    id: 'scyra',
    category: 'Strategy & Innovation AI Agents Suite',
    description:
      'Drive strategy, spark innovation, and align teams effectively.',
    image: scyra,
  },
  {
    name: 'Obed',
    id: 'obed',
    category: 'Sales Operations AI Agents Suite',
    description: 'Engage leads, schedule calls, and accelerate deal cycles.',
    image: liora,
  },
  {
    name: 'Compton',
    id: 'compton',
    category: 'Project & Program Oversight AI Agents Suite',
    description:
      'Track progress, manage projects, and deliver results on time.',
    image: colin,
  },
  {
    name: 'Colton',
    id: 'colton',
    category: 'Collections Coordination & Manager',
    description:
      'I ensure operations adhere to legal, ethical, and brand standards.',
    image: vesa,
  },
];

export const marketplaceAgents = [
  {
    name: 'Scyra',
    id: 'scyra',
    role: 'Smart Collections Communication',
    description: 'I message, resolve, and escalate when needed.',
    category: 'Collections Communication',
    image: scyra,
    features: [
      'Drives proactive, multi-channel outreach daily',
      'Reads and classifies all customer replies',
      'Learns, adapts, and improves message performance',
    ],
  },
  {
    name: 'Liora',
    id: 'liora',
    role: 'Legal Document Automation',
    description: 'I draft court-ready legal documents fast.',
    category: 'Legal Document Automation',
    image: liora,
    features: [
      'Generates court-ready Summons & Complaint Packages',
      'Surfaces high-impact recovery opportunities',
      'Continuously refines prioritization with predictive signals',
    ],
  },
  {
    name: 'Compton',
    id: 'compton',
    role: 'Collections Scoring',
    description: 'I score recovery likelihood and legal fit.',
    category: 'Resolution Scoring',
    image: colin,
    features: [
      'Maintains full history of all customer interactions',
      'Ensures availability across agents and campaigns',
      'Flags inconsistencies and supports investigations',
    ],
  },
  {
    name: 'Obed',
    id: 'obed',
    role: 'Voice Sentiment Analysis',
    description: 'I detect tone and trigger escalation.',
    category: 'Voice Sentiment Analysis',
    image: liora,
    features: [
      'Analyzes real-time voice tone for stress and intent',
      'Flags calls needing escalation to human agents',
      'Improves customer experience through empathy detection',
    ],
  },
  {
    name: 'Seto',
    id: 'seto',
    role: 'Settlement Planning Agent',
    description: 'I recommend the right settlement terms.',
    category: 'Settlement Planning',
    image: seto,
    features: [
      'Delivers tailored settlement offers with precision',
      'Tracks agent workflow and customer outcomes',
      'Aligns every step to strategy and compliance',
    ],
  },
  {
    name: 'Colton',
    id: 'colton',
    role: 'Collections Object Creation Agent',
    description: 'I detect tone and trigger escalation.',
    category: 'Collections Object Creation',
    image: vesa,
    suite: 'set-iq',
    features: [
      'Advanced object creation and management',
      'Integrates with multiple data sources',
      'Provides real-time object status updates',
    ],
  },
];

export const agentCategories = [
  { id: 'COLLECTION_SERVICES', alias: 'Collections Services' },
  { id: 'SALES_OPERATIONS', alias: 'Sales Operations' },
  { id: 'STRATEGY_AND_INNOVATION', alias: 'Strategy & Innovation' },
  {
    id: 'PROJECT_AND_PROGRAM_MANAGEMENT',
    alias: 'Project & Program Management',
  },
];

export const featureIcons = [starIcon, rhombusIcon, cubeIcon];
